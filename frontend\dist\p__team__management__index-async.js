((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__team__management__index'],
{ "src/pages/team/management/components/FriendSelector.tsx": function (module, exports, __mako_require__){
/**
 * 好友选择器组件
 * 
 * 功能特性：
 * - 从好友列表中选择并添加新的团队成员
 * - 支持多选和搜索功能
 * - 过滤已经是团队成员的好友
 * - 提供直观的选择界面
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
/**
 * 好友选择器组件
 */ const FriendSelector = ({ visible, onCancel, onSuccess, existingMemberIds })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [adding, setAdding] = (0, _react.useState)(false);
    const [friends, setFriends] = (0, _react.useState)([]);
    const [searchText, setSearchText] = (0, _react.useState)('');
    const [selectedFriendIds, setSelectedFriendIds] = (0, _react.useState)([]);
    /**
   * 获取好友列表
   */ const fetchFriends = async ()=>{
        try {
            setLoading(true);
            const friendList = await _services.FriendService.getFriends();
            setFriends(friendList);
        } catch (error) {
            console.error('获取好友列表失败:', error);
            _antd.message.error('获取好友列表失败');
        } finally{
            setLoading(false);
        }
    };
    (0, _react.useEffect)(()=>{
        if (visible) {
            fetchFriends();
            setSelectedFriendIds([]);
            setSearchText('');
        }
    }, [
        visible
    ]);
    // 过滤可选择的好友（排除已经是团队成员的好友）
    const availableFriends = friends.filter((friend)=>!existingMemberIds.includes(friend.id));
    // 根据搜索文本过滤好友
    const filteredFriends = availableFriends.filter((friend)=>!searchText || friend.name.toLowerCase().includes(searchText.toLowerCase()) || friend.email.toLowerCase().includes(searchText.toLowerCase()) || friend.remark && friend.remark.toLowerCase().includes(searchText.toLowerCase()));
    /**
   * 处理好友选择
   */ const handleFriendSelect = (friendId, checked)=>{
        if (checked) setSelectedFriendIds([
            ...selectedFriendIds,
            friendId
        ]);
        else setSelectedFriendIds(selectedFriendIds.filter((id)=>id !== friendId));
    };
    /**
   * 全选/取消全选
   */ const handleSelectAll = (checked)=>{
        if (checked) setSelectedFriendIds(filteredFriends.map((friend)=>friend.id));
        else setSelectedFriendIds([]);
    };
    /**
   * 添加选中的好友到团队
   */ const handleAddFriends = async ()=>{
        if (selectedFriendIds.length === 0) {
            _antd.message.warning('请选择要添加的好友');
            return;
        }
        try {
            setAdding(true);
            await _services.TeamService.assignFriendsToTeam(selectedFriendIds);
            _antd.message.success(`成功添加 ${selectedFriendIds.length} 名好友到团队`);
            onSuccess();
        } catch (error) {
            console.error('添加好友到团队失败:', error);
            _antd.message.error('添加好友到团队失败');
        } finally{
            setAdding(false);
        }
    };
    const isAllSelected = filteredFriends.length > 0 && selectedFriendIds.length === filteredFriends.length;
    const isIndeterminate = selectedFriendIds.length > 0 && selectedFriendIds.length < filteredFriends.length;
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: "添加团队成员",
        open: visible,
        onCancel: onCancel,
        width: 600,
        footer: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                onClick: onCancel,
                children: "取消"
            }, "cancel", false, {
                fileName: "src/pages/team/management/components/FriendSelector.tsx",
                lineNumber: 150,
                columnNumber: 9
            }, void 0),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                type: "primary",
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                    fileName: "src/pages/team/management/components/FriendSelector.tsx",
                    lineNumber: 156,
                    columnNumber: 17
                }, void 0),
                loading: adding,
                disabled: selectedFriendIds.length === 0,
                onClick: handleAddFriends,
                children: [
                    "添加选中的好友 (",
                    selectedFriendIds.length,
                    ")"
                ]
            }, "add", true, {
                fileName: "src/pages/team/management/components/FriendSelector.tsx",
                lineNumber: 153,
                columnNumber: 9
            }, void 0)
        ],
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                    placeholder: "搜索好友姓名、邮箱或备注",
                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/management/components/FriendSelector.tsx",
                        lineNumber: 168,
                        columnNumber: 19
                    }, void 0),
                    value: searchText,
                    onChange: (e)=>setSearchText(e.target.value),
                    allowClear: true
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/FriendSelector.tsx",
                    lineNumber: 166,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/management/components/FriendSelector.tsx",
                lineNumber: 165,
                columnNumber: 7
            }, this),
            filteredFriends.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Checkbox, {
                    indeterminate: isIndeterminate,
                    checked: isAllSelected,
                    onChange: (e)=>handleSelectAll(e.target.checked),
                    children: [
                        "全选 (",
                        filteredFriends.length,
                        " 个好友)"
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/management/components/FriendSelector.tsx",
                    lineNumber: 177,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/management/components/FriendSelector.tsx",
                lineNumber: 176,
                columnNumber: 9
            }, this),
            availableFriends.length === 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                description: "没有可添加的好友",
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "所有好友都已经是团队成员，或者您还没有添加任何好友。"
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/FriendSelector.tsx",
                    lineNumber: 192,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/management/components/FriendSelector.tsx",
                lineNumber: 188,
                columnNumber: 9
            }, this) : filteredFriends.length === 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                description: "没有找到匹配的好友",
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "请尝试使用其他关键词搜索。"
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/FriendSelector.tsx",
                    lineNumber: 201,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/management/components/FriendSelector.tsx",
                lineNumber: 197,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                loading: loading,
                dataSource: filteredFriends,
                renderItem: (friend)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                            avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Checkbox, {
                                checked: selectedFriendIds.includes(friend.id),
                                onChange: (e)=>handleFriendSelect(friend.id, e.target.checked)
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/FriendSelector.tsx",
                                lineNumber: 213,
                                columnNumber: 19
                            }, void 0),
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                        size: "small",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/management/components/FriendSelector.tsx",
                                            lineNumber: 220,
                                            columnNumber: 48
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/management/components/FriendSelector.tsx",
                                        lineNumber: 220,
                                        columnNumber: 21
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                        children: friend.name
                                    }, void 0, false, {
                                        fileName: "src/pages/team/management/components/FriendSelector.tsx",
                                        lineNumber: 221,
                                        columnNumber: 21
                                    }, void 0),
                                    friend.remark && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: [
                                            "(",
                                            friend.remark,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team/management/components/FriendSelector.tsx",
                                        lineNumber: 223,
                                        columnNumber: 23
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/management/components/FriendSelector.tsx",
                                lineNumber: 219,
                                columnNumber: 19
                            }, void 0),
                            description: friend.email
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/FriendSelector.tsx",
                            lineNumber: 211,
                            columnNumber: 15
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/team/management/components/FriendSelector.tsx",
                        lineNumber: 210,
                        columnNumber: 13
                    }, void 0),
                style: {
                    maxHeight: 400,
                    overflowY: 'auto'
                }
            }, void 0, false, {
                fileName: "src/pages/team/management/components/FriendSelector.tsx",
                lineNumber: 206,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/management/components/FriendSelector.tsx",
        lineNumber: 144,
        columnNumber: 5
    }, this);
};
_s(FriendSelector, "UQGTREgB5IApSRoyp0QRELaRIW0=");
_c = FriendSelector;
var _default = FriendSelector;
var _c;
$RefreshReg$(_c, "FriendSelector");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/management/components/MemberList.tsx": function (module, exports, __mako_require__){
/**
 * 成员列表组件
 * 
 * 功能特性：
 * - 展示团队成员列表
 * - 支持成员移除操作
 * - 批量操作支持
 * - 权限控制
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
/**
 * 成员列表组件
 */ const MemberList = ({ members, loading, isCreator, onMemberChange })=>{
    _s();
    const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
    /**
   * 移除单个成员
   */ const handleRemoveMember = (member)=>{
        if (member.isCreator) {
            _antd.message.warning('不能移除团队创建者');
            return;
        }
        _antd.Modal.confirm({
            title: '确认移除成员',
            content: `确定要移除成员 "${member.name}" 吗？`,
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                fileName: "src/pages/team/management/components/MemberList.tsx",
                lineNumber: 67,
                columnNumber: 13
            }, this),
            okText: '确认移除',
            cancelText: '取消',
            okType: 'danger',
            onOk: async ()=>{
                try {
                    await _services.TeamService.removeMember(member.id);
                    _antd.message.success('成员移除成功');
                    onMemberChange();
                } catch (error) {
                    console.error('移除成员失败:', error);
                    _antd.message.error('移除成员失败');
                }
            }
        });
    };
    /**
   * 批量移除成员
   */ const handleBatchRemove = ()=>{
        const selectedMembers = members.filter((member)=>selectedRowKeys.includes(member.id) && !member.isCreator);
        if (selectedMembers.length === 0) {
            _antd.message.warning('请选择要移除的成员');
            return;
        }
        _antd.Modal.confirm({
            title: '批量移除成员',
            content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                fileName: "src/pages/team/management/components/MemberList.tsx",
                lineNumber: 100,
                columnNumber: 13
            }, this),
            okText: '确认移除',
            cancelText: '取消',
            okType: 'danger',
            onOk: async ()=>{
                try {
                    await Promise.all(selectedMembers.map((member)=>_services.TeamService.removeMember(member.id)));
                    _antd.message.success(`成功移除 ${selectedMembers.length} 名成员`);
                    setSelectedRowKeys([]);
                    onMemberChange();
                } catch (error) {
                    console.error('批量移除成员失败:', error);
                    _antd.message.error('批量移除失败');
                }
            }
        });
    };
    const columns = [
        {
            title: '成员',
            dataIndex: 'name',
            key: 'name',
            render: (name, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/management/components/MemberList.tsx",
                                lineNumber: 129,
                                columnNumber: 38
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/MemberList.tsx",
                            lineNumber: 129,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: name
                                }, void 0, false, {
                                    fileName: "src/pages/team/management/components/MemberList.tsx",
                                    lineNumber: 131,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        fontSize: 12,
                                        color: '#999'
                                    },
                                    children: record.email
                                }, void 0, false, {
                                    fileName: "src/pages/team/management/components/MemberList.tsx",
                                    lineNumber: 132,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/management/components/MemberList.tsx",
                            lineNumber: 130,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/management/components/MemberList.tsx",
                    lineNumber: 128,
                    columnNumber: 9
                }, this)
        },
        {
            title: '角色',
            dataIndex: 'isCreator',
            key: 'role',
            width: 100,
            render: (isCreator)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isCreator ? 'gold' : 'blue',
                    icon: isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/management/components/MemberList.tsx",
                        lineNumber: 145,
                        columnNumber: 29
                    }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/management/components/MemberList.tsx",
                        lineNumber: 145,
                        columnNumber: 49
                    }, void 0),
                    children: isCreator ? '创建者' : '成员'
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/MemberList.tsx",
                    lineNumber: 143,
                    columnNumber: 9
                }, this)
        },
        {
            title: '状态',
            dataIndex: 'isActive',
            key: 'status',
            width: 80,
            render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isActive ? 'green' : 'red',
                    children: isActive ? '活跃' : '停用'
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/MemberList.tsx",
                    lineNumber: 157,
                    columnNumber: 9
                }, this)
        },
        {
            title: '加入时间',
            dataIndex: 'assignedAt',
            key: 'assignedAt',
            width: 150,
            render: (assignedAt)=>new Date(assignedAt).toLocaleDateString()
        },
        {
            title: '最后访问',
            dataIndex: 'lastAccessTime',
            key: 'lastAccessTime',
            width: 150,
            render: (lastAccessTime)=>{
                const date = new Date(lastAccessTime);
                const now = new Date();
                const diffDays = Math.floor((now.getTime() - date.getTime()) / 86400000);
                let color = 'green';
                if (diffDays > 7) color = 'orange';
                if (diffDays > 30) color = 'red';
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: date.toLocaleString(),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                        color: color,
                        children: diffDays === 0 ? '今天' : `${diffDays}天前`
                    }, void 0, false, {
                        fileName: "src/pages/team/management/components/MemberList.tsx",
                        lineNumber: 187,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/MemberList.tsx",
                    lineNumber: 186,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (_, record)=>{
                if (!isCreator || record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "-"
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/MemberList.tsx",
                    lineNumber: 200,
                    columnNumber: 18
                }, this);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "text",
                    danger: true,
                    size: "small",
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/management/components/MemberList.tsx",
                        lineNumber: 208,
                        columnNumber: 19
                    }, void 0),
                    onClick: ()=>handleRemoveMember(record),
                    children: "移除"
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/MemberList.tsx",
                    lineNumber: 204,
                    columnNumber: 11
                }, this);
            }
        }
    ];
    const rowSelection = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys)=>{
            setSelectedRowKeys(newSelectedRowKeys);
        },
        getCheckboxProps: (record)=>({
                disabled: record.isCreator
            })
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    strong: true,
                    children: "成员列表"
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/MemberList.tsx",
                    lineNumber: 232,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: [
                        "(",
                        members.length,
                        " 人)"
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/management/components/MemberList.tsx",
                    lineNumber: 233,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/management/components/MemberList.tsx",
            lineNumber: 231,
            columnNumber: 9
        }, void 0),
        children: [
            selectedRowKeys.length > 0 && isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16,
                    padding: 12,
                    background: '#f5f5f5',
                    borderRadius: 6
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            children: [
                                "已选择 ",
                                selectedRowKeys.length,
                                " 名成员"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/management/components/MemberList.tsx",
                            lineNumber: 247,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/management/components/MemberList.tsx",
                                lineNumber: 251,
                                columnNumber: 21
                            }, void 0),
                            onClick: handleBatchRemove,
                            children: "批量移除"
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/MemberList.tsx",
                            lineNumber: 248,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            onClick: ()=>setSelectedRowKeys([]),
                            children: "取消选择"
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/MemberList.tsx",
                            lineNumber: 256,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/management/components/MemberList.tsx",
                    lineNumber: 246,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/management/components/MemberList.tsx",
                lineNumber: 238,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                columns: columns,
                dataSource: members,
                rowKey: "id",
                loading: loading,
                rowSelection: isCreator ? rowSelection : undefined,
                pagination: {
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total)=>`共 ${total} 名成员`,
                    pageSize: 10
                }
            }, void 0, false, {
                fileName: "src/pages/team/management/components/MemberList.tsx",
                lineNumber: 263,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/management/components/MemberList.tsx",
        lineNumber: 229,
        columnNumber: 5
    }, this);
};
_s(MemberList, "AGfOs+7sjQarz8vBaL7lsXNTa7o=");
_c = MemberList;
var _default = MemberList;
var _c;
$RefreshReg$(_c, "MemberList");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/management/components/TeamMemberManagement.tsx": function (module, exports, __mako_require__){
/**
 * 团队成员管理组件
 * 
 * 功能特性：
 * - 查看团队成员列表及详细信息
 * - 添加新成员（从好友列表选择）
 * - 移除现有成员
 * - 支持成员搜索和筛选
 * - 批量操作支持
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var _FriendSelector = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/management/components/FriendSelector.tsx"));
var _MemberList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/management/components/MemberList.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { Option } = _antd.Select;
/**
 * 团队成员管理组件
 */ const TeamMemberManagement = ({ teamDetail, onTeamChange })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [members, setMembers] = (0, _react.useState)([]);
    const [searchText, setSearchText] = (0, _react.useState)('');
    const [statusFilter, setStatusFilter] = (0, _react.useState)('all');
    const [friendSelectorVisible, setFriendSelectorVisible] = (0, _react.useState)(false);
    /**
   * 获取团队成员列表
   */ const fetchMembers = async ()=>{
        if (!teamDetail) return;
        try {
            setLoading(true);
            const response = await _services.TeamService.getTeamMembers({
                current: 1,
                pageSize: 1000
            });
            setMembers((response === null || response === void 0 ? void 0 : response.list) || []);
        } catch (error) {
            console.error('获取团队成员失败:', error);
            _antd.message.error('获取团队成员失败');
            setMembers([]);
        } finally{
            setLoading(false);
        }
    };
    (0, _react.useEffect)(()=>{
        fetchMembers();
    }, [
        teamDetail
    ]);
    /**
   * 处理添加成员成功
   */ const handleAddMemberSuccess = ()=>{
        setFriendSelectorVisible(false);
        fetchMembers();
        onTeamChange();
        _antd.message.success('成员添加成功');
    };
    /**
   * 处理成员变更（移除等操作）
   */ const handleMemberChange = ()=>{
        fetchMembers();
        onTeamChange();
    };
    // 过滤成员列表
    const filteredMembers = members.filter((member)=>{
        if (!member || !member.name || !member.email) return false;
        // 搜索文本匹配
        const matchesSearch = !searchText || member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase());
        // 状态筛选匹配
        const matchesStatus = statusFilter === 'all' || statusFilter === 'active' && member.isActive || statusFilter === 'inactive' && !member.isActive || statusFilter === 'creator' && member.isCreator || statusFilter === 'member' && !member.isCreator;
        return matchesSearch && matchesStatus;
    });
    // 统计数据
    const activeMembers = members.filter((m)=>m.isActive).length;
    const totalMembers = members.length;
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                gutter: [
                    16,
                    16
                ],
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 8,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "总成员数",
                                value: totalMembers,
                                suffix: "人",
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                    style: {
                                        color: '#1890ff'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                    lineNumber: 140,
                                    columnNumber: 23
                                }, void 0),
                                valueStyle: {
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                lineNumber: 136,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                        lineNumber: 134,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 8,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "活跃成员",
                                value: activeMembers,
                                suffix: "人",
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                    style: {
                                        color: '#52c41a'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                    lineNumber: 151,
                                    columnNumber: 23
                                }, void 0),
                                valueStyle: {
                                    color: '#52c41a'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                lineNumber: 147,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                            lineNumber: 146,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                        lineNumber: 145,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 8,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "成员活跃率",
                                value: totalMembers > 0 ? Math.round(activeMembers / totalMembers * 100) : 0,
                                suffix: "%",
                                valueStyle: {
                                    color: activeMembers / totalMembers >= 0.8 ? '#52c41a' : activeMembers / totalMembers >= 0.6 ? '#faad14' : '#ff4d4f'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                lineNumber: 158,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                            lineNumber: 157,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                        lineNumber: 156,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                lineNumber: 133,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                style: {
                    marginBottom: 16
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    justify: "space-between",
                    align: "middle",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "搜索成员姓名或邮箱",
                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                            lineNumber: 178,
                                            columnNumber: 25
                                        }, void 0),
                                        value: searchText,
                                        onChange: (e)=>setSearchText(e.target.value),
                                        style: {
                                            width: 250
                                        },
                                        allowClear: true
                                    }, void 0, false, {
                                        fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                        lineNumber: 176,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                        value: statusFilter,
                                        onChange: setStatusFilter,
                                        style: {
                                            width: 120
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                                value: "all",
                                                children: "全部"
                                            }, void 0, false, {
                                                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                                lineNumber: 189,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                                value: "active",
                                                children: "活跃"
                                            }, void 0, false, {
                                                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                                lineNumber: 190,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                                value: "inactive",
                                                children: "停用"
                                            }, void 0, false, {
                                                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                                lineNumber: 191,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                                value: "creator",
                                                children: "创建者"
                                            }, void 0, false, {
                                                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                                lineNumber: 192,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                                value: "member",
                                                children: "成员"
                                            }, void 0, false, {
                                                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                                lineNumber: 193,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                        lineNumber: 184,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                lineNumber: 175,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                            lineNumber: 174,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                            lineNumber: 200,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: fetchMembers,
                                        loading: loading,
                                        children: "刷新"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                        lineNumber: 199,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                            lineNumber: 208,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: ()=>setFriendSelectorVisible(true),
                                        children: "添加成员"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                        lineNumber: 206,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                                lineNumber: 198,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                            lineNumber: 197,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                    lineNumber: 173,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                lineNumber: 172,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_MemberList.default, {
                members: filteredMembers,
                loading: loading,
                isCreator: (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.isCreator) || false,
                onMemberChange: handleMemberChange
            }, void 0, false, {
                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                lineNumber: 219,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FriendSelector.default, {
                visible: friendSelectorVisible,
                onCancel: ()=>setFriendSelectorVisible(false),
                onSuccess: handleAddMemberSuccess,
                existingMemberIds: members.map((m)=>m.accountId)
            }, void 0, false, {
                fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
                lineNumber: 227,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/management/components/TeamMemberManagement.tsx",
        lineNumber: 131,
        columnNumber: 5
    }, this);
};
_s(TeamMemberManagement, "UlbaXXQkyUrcq5qLzs6RUfkfHCc=");
_c = TeamMemberManagement;
var _default = TeamMemberManagement;
var _c;
$RefreshReg$(_c, "TeamMemberManagement");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/management/components/TeamSettings.tsx": function (module, exports, __mako_require__){
/**
 * 团队设置组件
 * 
 * 功能特性：
 * - 编辑团队名称和描述
 * - 删除整个团队
 * - 适当的确认对话框
 * - 权限控制
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const { TextArea } = _antd.Input;
/**
 * 团队设置组件
 */ const TeamSettings = ({ teamDetail, onTeamChange })=>{
    _s();
    const { setInitialState } = (0, _max.useModel)('@@initialState');
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [updating, setUpdating] = (0, _react.useState)(false);
    const [deleting, setDeleting] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    if (!teamDetail) return null;
    /**
   * 格式化日期
   */ const formatDate = (dateString)=>{
        return new Date(dateString).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };
    /**
   * 处理编辑团队信息
   */ const handleEdit = ()=>{
        form.setFieldsValue({
            name: teamDetail.name,
            description: teamDetail.description || ''
        });
        setEditModalVisible(true);
    };
    /**
   * 处理团队信息更新
   */ const handleUpdateTeam = async (values)=>{
        try {
            setUpdating(true);
            await _services.TeamService.updateCurrentTeam(values);
            _antd.message.success('团队信息更新成功');
            setEditModalVisible(false);
            onTeamChange();
        } catch (error) {
            console.error('更新团队失败:', error);
            _antd.message.error('更新团队失败');
        } finally{
            setUpdating(false);
        }
    };
    /**
   * 处理删除团队
   */ const handleDeleteTeam = ()=>{
        _antd.Modal.confirm({
            title: '确认删除团队',
            content: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                        children: [
                            "确定要删除团队 ",
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                strong: true,
                                children: [
                                    '"',
                                    teamDetail.name,
                                    '"'
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 115,
                                columnNumber: 21
                            }, this),
                            " 吗？"
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/management/components/TeamSettings.tsx",
                        lineNumber: 114,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                        type: "danger",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 118,
                                columnNumber: 13
                            }, this),
                            " 此操作不可恢复，将会："
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/management/components/TeamSettings.tsx",
                        lineNumber: 117,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                children: "永久删除团队及所有相关数据"
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 121,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                children: "移除所有团队成员"
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 122,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                children: "清除团队的所有设置和配置"
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 123,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/management/components/TeamSettings.tsx",
                        lineNumber: 120,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                lineNumber: 113,
                columnNumber: 9
            }, this),
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                style: {
                    color: '#ff4d4f'
                }
            }, void 0, false, {
                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                lineNumber: 127,
                columnNumber: 13
            }, this),
            okText: '确认删除',
            cancelText: '取消',
            okType: 'danger',
            width: 500,
            onOk: async ()=>{
                try {
                    setDeleting(true);
                    await _services.TeamService.deleteCurrentTeam();
                    _antd.message.success('团队删除成功');
                    // 更新全局状态，清除当前团队
                    setInitialState((s)=>({
                            ...s,
                            currentTeam: undefined
                        }));
                    // 跳转到团队选择页面
                    _max.history.push('/user/team-select');
                } catch (error) {
                    console.error('删除团队失败:', error);
                    _antd.message.error('删除团队失败');
                } finally{
                    setDeleting(false);
                }
            }
        });
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                gutter: [
                    16,
                    16
                ],
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 8,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "团队成员",
                                value: teamDetail.memberCount,
                                suffix: "人",
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                    style: {
                                        color: '#1890ff'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                    lineNumber: 161,
                                    columnNumber: 23
                                }, void 0),
                                valueStyle: {
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 157,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 156,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/management/components/TeamSettings.tsx",
                        lineNumber: 155,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 8,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "创建时间",
                                value: formatDate(teamDetail.createdAt),
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                    style: {
                                        color: '#52c41a'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                    lineNumber: 171,
                                    columnNumber: 23
                                }, void 0),
                                valueStyle: {
                                    color: '#52c41a',
                                    fontSize: 16
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 168,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 167,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/management/components/TeamSettings.tsx",
                        lineNumber: 166,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 8,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "最后更新",
                                value: formatDate(teamDetail.updatedAt),
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                    style: {
                                        color: '#faad14'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                    lineNumber: 181,
                                    columnNumber: 23
                                }, void 0),
                                valueStyle: {
                                    color: '#faad14',
                                    fontSize: 16
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 178,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 177,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/management/components/TeamSettings.tsx",
                        lineNumber: 176,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                lineNumber: 154,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 192,
                            columnNumber: 13
                        }, void 0),
                        "团队信息"
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/management/components/TeamSettings.tsx",
                    lineNumber: 191,
                    columnNumber: 11
                }, void 0),
                extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/management/components/TeamSettings.tsx",
                        lineNumber: 199,
                        columnNumber: 19
                    }, void 0),
                    onClick: handleEdit,
                    children: "编辑信息"
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/TeamSettings.tsx",
                    lineNumber: 197,
                    columnNumber: 11
                }, void 0),
                style: {
                    marginBottom: 24
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
                    column: 1,
                    bordered: true,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                            label: "团队名称",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                strong: true,
                                children: teamDetail.name
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 209,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 208,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                            label: "团队描述",
                            children: teamDetail.description || /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "暂无描述"
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 213,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 211,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                            label: "团队ID",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                code: true,
                                children: teamDetail.id
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 217,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 216,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                            label: "创建者",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: teamDetail.isCreator ? '您' : '其他用户'
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 220,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 219,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/management/components/TeamSettings.tsx",
                    lineNumber: 207,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                lineNumber: 189,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                            style: {
                                color: '#ff4d4f'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 229,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "danger",
                            children: "危险操作"
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 230,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/management/components/TeamSettings.tsx",
                    lineNumber: 228,
                    columnNumber: 11
                }, void 0),
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    direction: "vertical",
                    size: "large",
                    style: {
                        width: '100%'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 5,
                                type: "danger",
                                children: "删除团队"
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 236,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                type: "secondary",
                                children: "删除团队将永久移除所有团队数据，包括成员信息、设置等。此操作不可恢复。"
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 237,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                danger: true,
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                    lineNumber: 242,
                                    columnNumber: 21
                                }, void 0),
                                loading: deleting,
                                onClick: handleDeleteTeam,
                                children: "删除团队"
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 240,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/management/components/TeamSettings.tsx",
                        lineNumber: 235,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/management/components/TeamSettings.tsx",
                    lineNumber: 234,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                lineNumber: 226,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑团队信息",
                open: editModalVisible,
                onCancel: ()=>setEditModalVisible(false),
                footer: null,
                width: 600,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    layout: "vertical",
                    onFinish: handleUpdateTeam,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                },
                                {
                                    max: 50,
                                    message: '团队名称不能超过50个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 269,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 261,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 200,
                                    message: '团队描述不能超过200个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述（可选）",
                                showCount: true,
                                maxLength: 200
                            }, void 0, false, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 276,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 271,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>setEditModalVisible(false),
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                        lineNumber: 285,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SaveOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                            lineNumber: 289,
                                            columnNumber: 23
                                        }, void 0),
                                        loading: updating,
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                        lineNumber: 286,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                                lineNumber: 284,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/management/components/TeamSettings.tsx",
                            lineNumber: 283,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/management/components/TeamSettings.tsx",
                    lineNumber: 260,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/management/components/TeamSettings.tsx",
                lineNumber: 253,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/management/components/TeamSettings.tsx",
        lineNumber: 152,
        columnNumber: 5
    }, this);
};
_s(TeamSettings, "B27xVwrBw7UdeMxItBsc2KpJwBg=", false, function() {
    return [
        _max.useModel,
        _antd.Form.useForm
    ];
});
_c = TeamSettings;
var _default = TeamSettings;
var _c;
$RefreshReg$(_c, "TeamSettings");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/management/index.tsx": function (module, exports, __mako_require__){
/**
 * 团队管理集成页面
 * 
 * 功能特性：
 * - 集成所有团队管理功能到一个统一界面
 * - 使用选项卡布局组织不同的管理操作
 * - 包含团队成员管理、团队设置等功能
 * - 支持权限控制，只有创建者可以进行管理操作
 * - 提供直观的用户界面和适当的错误处理
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var _TeamMemberManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/management/components/TeamMemberManagement.tsx"));
var _TeamSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/management/components/TeamSettings.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
/**
 * 团队管理集成页面组件
 */ const TeamManagementPage = ()=>{
    _s();
    const { initialState } = (0, _max.useModel)('@@initialState');
    const [loading, setLoading] = (0, _react.useState)(true);
    const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
    const [activeTab, setActiveTab] = (0, _react.useState)('members');
    /**
   * 获取当前团队详情
   */ const fetchTeamDetail = async ()=>{
        try {
            setLoading(true);
            const detail = await _services.TeamService.getCurrentTeamDetail();
            setTeamDetail(detail);
        } catch (error) {
            console.error('获取团队详情失败:', error);
            _antd.message.error('获取团队详情失败');
        } finally{
            setLoading(false);
        }
    };
    (0, _react.useEffect)(()=>{
        fetchTeamDetail();
    }, []);
    // 检查用户是否有管理权限
    const hasManagePermission = (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.isCreator) || false;
    // 如果没有团队信息，显示提示
    if (!loading && !teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "团队管理",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
            message: "无法获取团队信息",
            description: "请确保您已选择了一个团队，并且有相应的访问权限。",
            type: "warning",
            showIcon: true
        }, void 0, false, {
            fileName: "src/pages/team/management/index.tsx",
            lineNumber: 72,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/management/index.tsx",
        lineNumber: 71,
        columnNumber: 7
    }, this);
    // 如果不是创建者，显示权限提示
    if (!loading && teamDetail && !hasManagePermission) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "团队管理",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
            message: "权限不足",
            description: "只有团队创建者可以进行团队管理操作。如需管理权限，请联系团队创建者。",
            type: "info",
            showIcon: true
        }, void 0, false, {
            fileName: "src/pages/team/management/index.tsx",
            lineNumber: 86,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/management/index.tsx",
        lineNumber: 85,
        columnNumber: 7
    }, this);
    // 构建选项卡项目
    const tabItems = [
        {
            key: 'members',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/management/index.tsx",
                        lineNumber: 102,
                        columnNumber: 11
                    }, this),
                    "成员管理"
                ]
            }, void 0, true, {
                fileName: "src/pages/team/management/index.tsx",
                lineNumber: 101,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberManagement.default, {
                teamDetail: teamDetail,
                onTeamChange: fetchTeamDetail
            }, void 0, false, {
                fileName: "src/pages/team/management/index.tsx",
                lineNumber: 107,
                columnNumber: 9
            }, this)
        },
        {
            key: 'settings',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/management/index.tsx",
                        lineNumber: 117,
                        columnNumber: 11
                    }, this),
                    "团队设置"
                ]
            }, void 0, true, {
                fileName: "src/pages/team/management/index.tsx",
                lineNumber: 116,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamSettings.default, {
                teamDetail: teamDetail,
                onTeamChange: fetchTeamDetail
            }, void 0, false, {
                fileName: "src/pages/team/management/index.tsx",
                lineNumber: 122,
                columnNumber: 9
            }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                    fileName: "src/pages/team/management/index.tsx",
                    lineNumber: 134,
                    columnNumber: 11
                }, void 0),
                "团队管理"
            ]
        }, void 0, true, {
            fileName: "src/pages/team/management/index.tsx",
            lineNumber: 133,
            columnNumber: 9
        }, void 0),
        subTitle: teamDetail ? `管理团队：${teamDetail.name}` : '',
        extra: (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.isCreator) && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {
                    style: {
                        color: '#faad14'
                    }
                }, void 0, false, {
                    fileName: "src/pages/team/management/index.tsx",
                    lineNumber: 142,
                    columnNumber: 13
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "管理员权限"
                }, void 0, false, {
                    fileName: "src/pages/team/management/index.tsx",
                    lineNumber: 143,
                    columnNumber: 13
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/management/index.tsx",
            lineNumber: 141,
            columnNumber: 11
        }, void 0),
        loading: loading,
        children: teamDetail && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: setActiveTab,
                items: tabItems,
                size: "large",
                tabBarStyle: {
                    marginBottom: 24
                }
            }, void 0, false, {
                fileName: "src/pages/team/management/index.tsx",
                lineNumber: 151,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/team/management/index.tsx",
            lineNumber: 150,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/management/index.tsx",
        lineNumber: 131,
        columnNumber: 5
    }, this);
};
_s(TeamManagementPage, "G2sPUJ/uctan0QsvsMpwz+MvGLo=", false, function() {
    return [
        _max.useModel
    ];
});
_c = TeamManagementPage;
var _default = TeamManagementPage;
var _c;
$RefreshReg$(_c, "TeamManagementPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__team__management__index-async.js.map