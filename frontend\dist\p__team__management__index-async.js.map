{"version": 3, "sources": ["src/pages/team/management/components/FriendSelector.tsx", "src/pages/team/management/components/MemberList.tsx", "src/pages/team/management/components/TeamMemberManagement.tsx", "src/pages/team/management/components/TeamSettings.tsx", "src/pages/team/management/index.tsx"], "sourcesContent": ["/**\n * 好友选择器组件\n * \n * 功能特性：\n * - 从好友列表中选择并添加新的团队成员\n * - 支持多选和搜索功能\n * - 过滤已经是团队成员的好友\n * - 提供直观的选择界面\n */\n\nimport {\n  SearchOutlined,\n  UserAddOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  Avatar,\n  Button,\n  Checkbox,\n  Empty,\n  Input,\n  List,\n  Modal,\n  Space,\n  Typography,\n  message,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { FriendService, TeamService } from '@/services';\nimport type { FriendWithRemark } from '@/types/api';\n\nconst { Text } = Typography;\n\ninterface FriendSelectorProps {\n  visible: boolean;\n  onCancel: () => void;\n  onSuccess: () => void;\n  existingMemberIds: number[];\n}\n\n/**\n * 好友选择器组件\n */\nconst FriendSelector: React.FC<FriendSelectorProps> = ({\n  visible,\n  onCancel,\n  onSuccess,\n  existingMemberIds,\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [adding, setAdding] = useState(false);\n  const [friends, setFriends] = useState<FriendWithRemark[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [selectedFriendIds, setSelectedFriendIds] = useState<number[]>([]);\n\n  /**\n   * 获取好友列表\n   */\n  const fetchFriends = async () => {\n    try {\n      setLoading(true);\n      const friendList = await FriendService.getFriends();\n      setFriends(friendList);\n    } catch (error) {\n      console.error('获取好友列表失败:', error);\n      message.error('获取好友列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    if (visible) {\n      fetchFriends();\n      setSelectedFriendIds([]);\n      setSearchText('');\n    }\n  }, [visible]);\n\n  // 过滤可选择的好友（排除已经是团队成员的好友）\n  const availableFriends = friends.filter(\n    (friend) => !existingMemberIds.includes(friend.id)\n  );\n\n  // 根据搜索文本过滤好友\n  const filteredFriends = availableFriends.filter(\n    (friend) =>\n      !searchText ||\n      friend.name.toLowerCase().includes(searchText.toLowerCase()) ||\n      friend.email.toLowerCase().includes(searchText.toLowerCase()) ||\n      (friend.remark && friend.remark.toLowerCase().includes(searchText.toLowerCase()))\n  );\n\n  /**\n   * 处理好友选择\n   */\n  const handleFriendSelect = (friendId: number, checked: boolean) => {\n    if (checked) {\n      setSelectedFriendIds([...selectedFriendIds, friendId]);\n    } else {\n      setSelectedFriendIds(selectedFriendIds.filter(id => id !== friendId));\n    }\n  };\n\n  /**\n   * 全选/取消全选\n   */\n  const handleSelectAll = (checked: boolean) => {\n    if (checked) {\n      setSelectedFriendIds(filteredFriends.map(friend => friend.id));\n    } else {\n      setSelectedFriendIds([]);\n    }\n  };\n\n  /**\n   * 添加选中的好友到团队\n   */\n  const handleAddFriends = async () => {\n    if (selectedFriendIds.length === 0) {\n      message.warning('请选择要添加的好友');\n      return;\n    }\n\n    try {\n      setAdding(true);\n      await TeamService.assignFriendsToTeam(selectedFriendIds);\n      message.success(`成功添加 ${selectedFriendIds.length} 名好友到团队`);\n      onSuccess();\n    } catch (error) {\n      console.error('添加好友到团队失败:', error);\n      message.error('添加好友到团队失败');\n    } finally {\n      setAdding(false);\n    }\n  };\n\n  const isAllSelected = filteredFriends.length > 0 && \n    selectedFriendIds.length === filteredFriends.length;\n  const isIndeterminate = selectedFriendIds.length > 0 && \n    selectedFriendIds.length < filteredFriends.length;\n\n  return (\n    <Modal\n      title=\"添加团队成员\"\n      open={visible}\n      onCancel={onCancel}\n      width={600}\n      footer={[\n        <Button key=\"cancel\" onClick={onCancel}>\n          取消\n        </Button>,\n        <Button\n          key=\"add\"\n          type=\"primary\"\n          icon={<UserAddOutlined />}\n          loading={adding}\n          disabled={selectedFriendIds.length === 0}\n          onClick={handleAddFriends}\n        >\n          添加选中的好友 ({selectedFriendIds.length})\n        </Button>,\n      ]}\n    >\n      <div style={{ marginBottom: 16 }}>\n        <Input\n          placeholder=\"搜索好友姓名、邮箱或备注\"\n          prefix={<SearchOutlined />}\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n          allowClear\n        />\n      </div>\n\n      {filteredFriends.length > 0 && (\n        <div style={{ marginBottom: 16 }}>\n          <Checkbox\n            indeterminate={isIndeterminate}\n            checked={isAllSelected}\n            onChange={(e) => handleSelectAll(e.target.checked)}\n          >\n            全选 ({filteredFriends.length} 个好友)\n          </Checkbox>\n        </div>\n      )}\n\n      {availableFriends.length === 0 ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description=\"没有可添加的好友\"\n        >\n          <Text type=\"secondary\">\n            所有好友都已经是团队成员，或者您还没有添加任何好友。\n          </Text>\n        </Empty>\n      ) : filteredFriends.length === 0 ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description=\"没有找到匹配的好友\"\n        >\n          <Text type=\"secondary\">\n            请尝试使用其他关键词搜索。\n          </Text>\n        </Empty>\n      ) : (\n        <List\n          loading={loading}\n          dataSource={filteredFriends}\n          renderItem={(friend) => (\n            <List.Item>\n              <List.Item.Meta\n                avatar={\n                  <Checkbox\n                    checked={selectedFriendIds.includes(friend.id)}\n                    onChange={(e) => handleFriendSelect(friend.id, e.target.checked)}\n                  />\n                }\n                title={\n                  <Space>\n                    <Avatar size=\"small\" icon={<UserOutlined />} />\n                    <span>{friend.name}</span>\n                    {friend.remark && (\n                      <Text type=\"secondary\">({friend.remark})</Text>\n                    )}\n                  </Space>\n                }\n                description={friend.email}\n              />\n            </List.Item>\n          )}\n          style={{ maxHeight: 400, overflowY: 'auto' }}\n        />\n      )}\n    </Modal>\n  );\n};\n\nexport default FriendSelector;\n", "/**\n * 成员列表组件\n * \n * 功能特性：\n * - 展示团队成员列表\n * - 支持成员移除操作\n * - 批量操作支持\n * - 权限控制\n */\n\nimport {\n  CheckCircleOutlined,\n  CrownOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  Avatar,\n  Button,\n  Card,\n  Modal,\n  Space,\n  Table,\n  Tag,\n  Tooltip,\n  Typography,\n  message,\n} from 'antd';\nimport type { ColumnsType } from 'antd/es/table';\nimport React, { useState } from 'react';\nimport { TeamService } from '@/services';\nimport type { TeamMemberResponse } from '@/types/api';\n\nconst { Text } = Typography;\n\ninterface MemberListProps {\n  members: TeamMemberResponse[];\n  loading: boolean;\n  isCreator: boolean;\n  onMemberChange: () => void;\n}\n\n/**\n * 成员列表组件\n */\nconst MemberList: React.FC<MemberListProps> = ({\n  members,\n  loading,\n  isCreator,\n  onMemberChange,\n}) => {\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n\n  /**\n   * 移除单个成员\n   */\n  const handleRemoveMember = (member: TeamMemberResponse) => {\n    if (member.isCreator) {\n      message.warning('不能移除团队创建者');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认移除成员',\n      content: `确定要移除成员 \"${member.name}\" 吗？`,\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认移除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          await TeamService.removeMember(member.id);\n          message.success('成员移除成功');\n          onMemberChange();\n        } catch (error) {\n          console.error('移除成员失败:', error);\n          message.error('移除成员失败');\n        }\n      },\n    });\n  };\n\n  /**\n   * 批量移除成员\n   */\n  const handleBatchRemove = () => {\n    const selectedMembers = members.filter(\n      (member) => selectedRowKeys.includes(member.id) && !member.isCreator,\n    );\n\n    if (selectedMembers.length === 0) {\n      message.warning('请选择要移除的成员');\n      return;\n    }\n\n    Modal.confirm({\n      title: '批量移除成员',\n      content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认移除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          await Promise.all(\n            selectedMembers.map((member) =>\n              TeamService.removeMember(member.id),\n            ),\n          );\n          message.success(`成功移除 ${selectedMembers.length} 名成员`);\n          setSelectedRowKeys([]);\n          onMemberChange();\n        } catch (error) {\n          console.error('批量移除成员失败:', error);\n          message.error('批量移除失败');\n        }\n      },\n    });\n  };\n\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '成员',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name, record) => (\n        <Space>\n          <Avatar size=\"small\" icon={<UserOutlined />} />\n          <div>\n            <div>{name}</div>\n            <div style={{ fontSize: 12, color: '#999' }}>{record.email}</div>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'isCreator',\n      key: 'role',\n      width: 100,\n      render: (isCreator) => (\n        <Tag\n          color={isCreator ? 'gold' : 'blue'}\n          icon={isCreator ? <CrownOutlined /> : <UserOutlined />}\n        >\n          {isCreator ? '创建者' : '成员'}\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 80,\n      render: (isActive) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '活跃' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (assignedAt) => new Date(assignedAt).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (lastAccessTime) => {\n        const date = new Date(lastAccessTime);\n        const now = new Date();\n        const diffDays = Math.floor(\n          (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24),\n        );\n\n        let color = 'green';\n        if (diffDays > 7) color = 'orange';\n        if (diffDays > 30) color = 'red';\n\n        return (\n          <Tooltip title={date.toLocaleString()}>\n            <Tag color={color}>\n              {diffDays === 0 ? '今天' : `${diffDays}天前`}\n            </Tag>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => {\n        if (!isCreator || record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        return (\n          <Button\n            type=\"text\"\n            danger\n            size=\"small\"\n            icon={<DeleteOutlined />}\n            onClick={() => handleRemoveMember(record)}\n          >\n            移除\n          </Button>\n        );\n      },\n    },\n  ];\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选中\n    }),\n  };\n\n  return (\n    <Card\n      title={\n        <Space>\n          <Text strong>成员列表</Text>\n          <Text type=\"secondary\">({members.length} 人)</Text>\n        </Space>\n      }\n    >\n      {selectedRowKeys.length > 0 && isCreator && (\n        <div\n          style={{\n            marginBottom: 16,\n            padding: 12,\n            background: '#f5f5f5',\n            borderRadius: 6,\n          }}\n        >\n          <Space>\n            <Text>已选择 {selectedRowKeys.length} 名成员</Text>\n            <Button\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={handleBatchRemove}\n            >\n              批量移除\n            </Button>\n            <Button size=\"small\" onClick={() => setSelectedRowKeys([])}>\n              取消选择\n            </Button>\n          </Space>\n        </div>\n      )}\n\n      <Table\n        columns={columns}\n        dataSource={members}\n        rowKey=\"id\"\n        loading={loading}\n        rowSelection={isCreator ? rowSelection : undefined}\n        pagination={{\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 名成员`,\n          pageSize: 10,\n        }}\n      />\n    </Card>\n  );\n};\n\nexport default MemberList;\n", "/**\n * 团队成员管理组件\n * \n * 功能特性：\n * - 查看团队成员列表及详细信息\n * - 添加新成员（从好友列表选择）\n * - 移除现有成员\n * - 支持成员搜索和筛选\n * - 批量操作支持\n */\n\nimport {\n  DeleteOutlined,\n  PlusOutlined,\n  ReloadOutlined,\n  SearchOutlined,\n  UserAddOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  Button,\n  Card,\n  Col,\n  Input,\n  Row,\n  Select,\n  Space,\n  Statistic,\n  Typography,\n  message,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, TeamMemberResponse } from '@/types/api';\nimport FriendSelector from './FriendSelector';\nimport MemberList from './MemberList';\n\nconst { Text } = Typography;\nconst { Option } = Select;\n\ninterface TeamMemberManagementProps {\n  teamDetail: TeamDetailResponse | null;\n  onTeamChange: () => void;\n}\n\n/**\n * 团队成员管理组件\n */\nconst TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({\n  teamDetail,\n  onTeamChange,\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n  const [friendSelectorVisible, setFriendSelectorVisible] = useState(false);\n\n  /**\n   * 获取团队成员列表\n   */\n  const fetchMembers = async () => {\n    if (!teamDetail) return;\n\n    try {\n      setLoading(true);\n      const response = await TeamService.getTeamMembers({\n        current: 1,\n        pageSize: 1000,\n      });\n      setMembers(response?.list || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      setMembers([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchMembers();\n  }, [teamDetail]);\n\n  /**\n   * 处理添加成员成功\n   */\n  const handleAddMemberSuccess = () => {\n    setFriendSelectorVisible(false);\n    fetchMembers();\n    onTeamChange();\n    message.success('成员添加成功');\n  };\n\n  /**\n   * 处理成员变更（移除等操作）\n   */\n  const handleMemberChange = () => {\n    fetchMembers();\n    onTeamChange();\n  };\n\n  // 过滤成员列表\n  const filteredMembers = members.filter((member) => {\n    if (!member || !member.name || !member.email) {\n      return false;\n    }\n\n    // 搜索文本匹配\n    const matchesSearch =\n      !searchText ||\n      member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n      member.email.toLowerCase().includes(searchText.toLowerCase());\n\n    // 状态筛选匹配\n    const matchesStatus =\n      statusFilter === 'all' ||\n      (statusFilter === 'active' && member.isActive) ||\n      (statusFilter === 'inactive' && !member.isActive) ||\n      (statusFilter === 'creator' && member.isCreator) ||\n      (statusFilter === 'member' && !member.isCreator);\n\n    return matchesSearch && matchesStatus;\n  });\n\n  // 统计数据\n  const activeMembers = members.filter((m) => m.isActive).length;\n  const totalMembers = members.length;\n\n  return (\n    <div>\n      {/* 统计信息 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={8} md={6}>\n          <Card>\n            <Statistic\n              title=\"总成员数\"\n              value={totalMembers}\n              suffix=\"人\"\n              prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={8} md={6}>\n          <Card>\n            <Statistic\n              title=\"活跃成员\"\n              value={activeMembers}\n              suffix=\"人\"\n              prefix={<UserOutlined style={{ color: '#52c41a' }} />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={8} md={6}>\n          <Card>\n            <Statistic\n              title=\"成员活跃率\"\n              value={totalMembers > 0 ? Math.round((activeMembers / totalMembers) * 100) : 0}\n              suffix=\"%\"\n              valueStyle={{ \n                color: activeMembers / totalMembers >= 0.8 ? '#52c41a' : \n                       activeMembers / totalMembers >= 0.6 ? '#faad14' : '#ff4d4f' \n              }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 操作栏 */}\n      <Card style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Input\n                placeholder=\"搜索成员姓名或邮箱\"\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ width: 250 }}\n                allowClear\n              />\n              <Select\n                value={statusFilter}\n                onChange={setStatusFilter}\n                style={{ width: 120 }}\n              >\n                <Option value=\"all\">全部</Option>\n                <Option value=\"active\">活跃</Option>\n                <Option value=\"inactive\">停用</Option>\n                <Option value=\"creator\">创建者</Option>\n                <Option value=\"member\">成员</Option>\n              </Select>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={fetchMembers}\n                loading={loading}\n              >\n                刷新\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<UserAddOutlined />}\n                onClick={() => setFriendSelectorVisible(true)}\n              >\n                添加成员\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 成员列表 */}\n      <MemberList\n        members={filteredMembers}\n        loading={loading}\n        isCreator={teamDetail?.isCreator || false}\n        onMemberChange={handleMemberChange}\n      />\n\n      {/* 好友选择器 */}\n      <FriendSelector\n        visible={friendSelectorVisible}\n        onCancel={() => setFriendSelectorVisible(false)}\n        onSuccess={handleAddMemberSuccess}\n        existingMemberIds={members.map(m => m.accountId)}\n      />\n    </div>\n  );\n};\n\nexport default TeamMemberManagement;\n", "/**\n * 团队设置组件\n * \n * 功能特性：\n * - 编辑团队名称和描述\n * - 删除整个团队\n * - 适当的确认对话框\n * - 权限控制\n */\n\nimport {\n  CalendarOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  ExclamationCircleOutlined,\n  SaveOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Col,\n  Descriptions,\n  Form,\n  Input,\n  Modal,\n  Row,\n  Space,\n  Statistic,\n  Typography,\n  message,\n} from 'antd';\nimport React, { useState } from 'react';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamSettingsProps {\n  teamDetail: TeamDetailResponse | null;\n  onTeamChange: () => void;\n}\n\n/**\n * 团队设置组件\n */\nconst TeamSettings: React.FC<TeamSettingsProps> = ({\n  teamDetail,\n  onTeamChange,\n}) => {\n  const { setInitialState } = useModel('@@initialState');\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n\n  if (!teamDetail) {\n    return null;\n  }\n\n  /**\n   * 格式化日期\n   */\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  /**\n   * 处理编辑团队信息\n   */\n  const handleEdit = () => {\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditModalVisible(true);\n  };\n\n  /**\n   * 处理团队信息更新\n   */\n  const handleUpdateTeam = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      await TeamService.updateCurrentTeam(values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      onTeamChange();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  /**\n   * 处理删除团队\n   */\n  const handleDeleteTeam = () => {\n    Modal.confirm({\n      title: '确认删除团队',\n      content: (\n        <div>\n          <Paragraph>\n            确定要删除团队 <Text strong>\"{teamDetail.name}\"</Text> 吗？\n          </Paragraph>\n          <Paragraph type=\"danger\">\n            <ExclamationCircleOutlined /> 此操作不可恢复，将会：\n          </Paragraph>\n          <ul>\n            <li>永久删除团队及所有相关数据</li>\n            <li>移除所有团队成员</li>\n            <li>清除团队的所有设置和配置</li>\n          </ul>\n        </div>\n      ),\n      icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '确认删除',\n      cancelText: '取消',\n      okType: 'danger',\n      width: 500,\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          await TeamService.deleteCurrentTeam();\n          message.success('团队删除成功');\n          // 更新全局状态，清除当前团队\n          setInitialState((s) => ({ ...s, currentTeam: undefined }));\n          // 跳转到团队选择页面\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  return (\n    <div>\n      {/* 团队基本信息 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={8}>\n          <Card>\n            <Statistic\n              title=\"团队成员\"\n              value={teamDetail.memberCount}\n              suffix=\"人\"\n              prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8}>\n          <Card>\n            <Statistic\n              title=\"创建时间\"\n              value={formatDate(teamDetail.createdAt)}\n              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}\n              valueStyle={{ color: '#52c41a', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8}>\n          <Card>\n            <Statistic\n              title=\"最后更新\"\n              value={formatDate(teamDetail.updatedAt)}\n              prefix={<CalendarOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 团队详细信息 */}\n      <Card\n        title={\n          <Space>\n            <TeamOutlined />\n            团队信息\n          </Space>\n        }\n        extra={\n          <Button\n            type=\"primary\"\n            icon={<EditOutlined />}\n            onClick={handleEdit}\n          >\n            编辑信息\n          </Button>\n        }\n        style={{ marginBottom: 24 }}\n      >\n        <Descriptions column={1} bordered>\n          <Descriptions.Item label=\"团队名称\">\n            <Text strong>{teamDetail.name}</Text>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"团队描述\">\n            {teamDetail.description || (\n              <Text type=\"secondary\">暂无描述</Text>\n            )}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"团队ID\">\n            <Text code>{teamDetail.id}</Text>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建者\">\n            <Text>{teamDetail.isCreator ? '您' : '其他用户'}</Text>\n          </Descriptions.Item>\n        </Descriptions>\n      </Card>\n\n      {/* 危险操作区域 */}\n      <Card\n        title={\n          <Space>\n            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />\n            <Text type=\"danger\">危险操作</Text>\n          </Space>\n        }\n      >\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Title level={5} type=\"danger\">删除团队</Title>\n            <Paragraph type=\"secondary\">\n              删除团队将永久移除所有团队数据，包括成员信息、设置等。此操作不可恢复。\n            </Paragraph>\n            <Button\n              danger\n              icon={<DeleteOutlined />}\n              loading={deleting}\n              onClick={handleDeleteTeam}\n            >\n              删除团队\n            </Button>\n          </div>\n        </Space>\n      </Card>\n\n      {/* 编辑团队信息模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form form={form} layout=\"vertical\" onFinish={handleUpdateTeam}>\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { max: 50, message: '团队名称不能超过50个字符' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[{ max: 200, message: '团队描述不能超过200个字符' }]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setEditModalVisible(false)}>取消</Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                icon={<SaveOutlined />}\n                loading={updating}\n              >\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamSettings;\n", "/**\n * 团队管理集成页面\n * \n * 功能特性：\n * - 集成所有团队管理功能到一个统一界面\n * - 使用选项卡布局组织不同的管理操作\n * - 包含团队成员管理、团队设置等功能\n * - 支持权限控制，只有创建者可以进行管理操作\n * - 提供直观的用户界面和适当的错误处理\n */\n\nimport {\n  CrownOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { useModel } from '@umijs/max';\nimport {\n  Alert,\n  Card,\n  Space,\n  Tabs,\n  Typography,\n  message,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\nimport TeamMemberManagement from './components/TeamMemberManagement';\nimport TeamSettings from './components/TeamSettings';\n\nconst { Title, Text } = Typography;\n\n/**\n * 团队管理集成页面组件\n */\nconst TeamManagementPage: React.FC = () => {\n  const { initialState } = useModel('@@initialState');\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const [activeTab, setActiveTab] = useState('members');\n\n  /**\n   * 获取当前团队详情\n   */\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      message.error('获取团队详情失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  // 检查用户是否有管理权限\n  const hasManagePermission = teamDetail?.isCreator || false;\n\n  // 如果没有团队信息，显示提示\n  if (!loading && !teamDetail) {\n    return (\n      <PageContainer title=\"团队管理\">\n        <Alert\n          message=\"无法获取团队信息\"\n          description=\"请确保您已选择了一个团队，并且有相应的访问权限。\"\n          type=\"warning\"\n          showIcon\n        />\n      </PageContainer>\n    );\n  }\n\n  // 如果不是创建者，显示权限提示\n  if (!loading && teamDetail && !hasManagePermission) {\n    return (\n      <PageContainer title=\"团队管理\">\n        <Alert\n          message=\"权限不足\"\n          description=\"只有团队创建者可以进行团队管理操作。如需管理权限，请联系团队创建者。\"\n          type=\"info\"\n          showIcon\n        />\n      </PageContainer>\n    );\n  }\n\n  // 构建选项卡项目\n  const tabItems = [\n    {\n      key: 'members',\n      label: (\n        <Space>\n          <UserOutlined />\n          成员管理\n        </Space>\n      ),\n      children: (\n        <TeamMemberManagement\n          teamDetail={teamDetail}\n          onTeamChange={fetchTeamDetail}\n        />\n      ),\n    },\n    {\n      key: 'settings',\n      label: (\n        <Space>\n          <SettingOutlined />\n          团队设置\n        </Space>\n      ),\n      children: (\n        <TeamSettings\n          teamDetail={teamDetail}\n          onTeamChange={fetchTeamDetail}\n        />\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer\n      title={\n        <Space>\n          <TeamOutlined />\n          团队管理\n        </Space>\n      }\n      subTitle={teamDetail ? `管理团队：${teamDetail.name}` : ''}\n      extra={\n        teamDetail?.isCreator && (\n          <Space>\n            <CrownOutlined style={{ color: '#faad14' }} />\n            <Text type=\"secondary\">管理员权限</Text>\n          </Space>\n        )\n      }\n      loading={loading}\n    >\n      {teamDetail && (\n        <Card>\n          <Tabs\n            activeKey={activeTab}\n            onChange={setActiveTab}\n            items={tabItems}\n            size=\"large\"\n            tabBarStyle={{ marginBottom: 24 }}\n          />\n        </Card>\n      )}\n    </PageContainer>\n  );\n};\n\nexport default TeamManagementPage;\n"], "names": [], "mappings": ";;;AAAA;;;;;;;;CAQC;;;;4BAqOD;;;eAAA;;;;;;8BA/NO;6BAYA;wEACoC;iCACA;;;;;;;;;;AAG3C,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAS3B;;CAEC,GACD,MAAM,iBAAgD,CAAC,EACrD,OAAO,EACP,QAAQ,EACR,SAAS,EACT,iBAAiB,EAClB;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAC;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAqB,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,eAAQ,EAAW,EAAE;IAEvE;;GAEC,GACD,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,aAAa,MAAM,uBAAa,CAAC,UAAU;YACjD,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAA,gBAAS,EAAC;QACR,IAAI,SAAS;YACX;YACA,qBAAqB,EAAE;YACvB,cAAc;QAChB;IACF,GAAG;QAAC;KAAQ;IAEZ,yBAAyB;IACzB,MAAM,mBAAmB,QAAQ,MAAM,CACrC,CAAC,SAAW,CAAC,kBAAkB,QAAQ,CAAC,OAAO,EAAE;IAGnD,aAAa;IACb,MAAM,kBAAkB,iBAAiB,MAAM,CAC7C,CAAC,SACC,CAAC,cACD,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGjF;;GAEC,GACD,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,IAAI,SACF,qBAAqB;eAAI;YAAmB;SAAS;aAErD,qBAAqB,kBAAkB,MAAM,CAAC,CAAA,KAAM,OAAO;IAE/D;IAEA;;GAEC,GACD,MAAM,kBAAkB,CAAC;QACvB,IAAI,SACF,qBAAqB,gBAAgB,GAAG,CAAC,CAAA,SAAU,OAAO,EAAE;aAE5D,qBAAqB,EAAE;IAE3B;IAEA;;GAEC,GACD,MAAM,mBAAmB;QACvB,IAAI,kBAAkB,MAAM,KAAK,GAAG;YAClC,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,IAAI;YACF,UAAU;YACV,MAAM,qBAAW,CAAC,mBAAmB,CAAC;YACtC,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,kBAAkB,MAAM,CAAC,OAAO,CAAC;YACzD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,gBAAgB,gBAAgB,MAAM,GAAG,KAC7C,kBAAkB,MAAM,KAAK,gBAAgB,MAAM;IACrD,MAAM,kBAAkB,kBAAkB,MAAM,GAAG,KACjD,kBAAkB,MAAM,GAAG,gBAAgB,MAAM;IAEnD,qBACE,2BAAC,WAAK;QACJ,OAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,QAAQ;0BACN,2BAAC,YAAM;gBAAc,SAAS;0BAAU;eAA5B;;;;;0BAGZ,2BAAC,YAAM;gBAEL,MAAK;gBACL,oBAAM,2BAAC,sBAAe;;;;;gBACtB,SAAS;gBACT,UAAU,kBAAkB,MAAM,KAAK;gBACvC,SAAS;;oBACV;oBACW,kBAAkB,MAAM;oBAAC;;eAP/B;;;;;SASP;;0BAED,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;0BAC7B,cAAA,2BAAC,WAAK;oBACJ,aAAY;oBACZ,sBAAQ,2BAAC,qBAAc;;;;;oBACvB,OAAO;oBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oBAC7C,UAAU;;;;;;;;;;;YAIb,gBAAgB,MAAM,GAAG,mBACxB,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;0BAC7B,cAAA,2BAAC,cAAQ;oBACP,eAAe;oBACf,SAAS;oBACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;;wBAClD;wBACM,gBAAgB,MAAM;wBAAC;;;;;;;;;;;;YAKjC,iBAAiB,MAAM,KAAK,kBAC3B,2BAAC,WAAK;gBACJ,OAAO,WAAK,CAAC,sBAAsB;gBACnC,aAAY;0BAEZ,cAAA,2BAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;uBAIvB,gBAAgB,MAAM,KAAK,kBAC7B,2BAAC,WAAK;gBACJ,OAAO,WAAK,CAAC,sBAAsB;gBACnC,aAAY;0BAEZ,cAAA,2BAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;qCAKzB,2BAAC,UAAI;gBACH,SAAS;gBACT,YAAY;gBACZ,YAAY,CAAC,uBACX,2BAAC,UAAI,CAAC,IAAI;kCACR,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;4BACb,sBACE,2BAAC,cAAQ;gCACP,SAAS,kBAAkB,QAAQ,CAAC,OAAO,EAAE;gCAC7C,UAAU,CAAC,IAAM,mBAAmB,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,OAAO;;;;;;4BAGnE,qBACE,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,MAAK;wCAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;kDACxC,2BAAC;kDAAM,OAAO,IAAI;;;;;;oCACjB,OAAO,MAAM,kBACZ,2BAAC;wCAAK,MAAK;;4CAAY;4CAAE,OAAO,MAAM;4CAAC;;;;;;;;;;;;;4BAI7C,aAAa,OAAO,KAAK;;;;;;;;;;;gBAI/B,OAAO;oBAAE,WAAW;oBAAK,WAAW;gBAAO;;;;;;;;;;;;AAKrD;GAhMM;KAAA;IAkMN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7Of;;;;;;;;CAQC;;;;4BA+QD;;;eAAA;;;;;;8BAvQO;6BAYA;wEAEyB;iCACJ;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAS3B;;CAEC,GACD,MAAM,aAAwC,CAAC,EAC7C,OAAO,EACP,OAAO,EACP,SAAS,EACT,cAAc,EACf;;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;IAEtE;;GAEC,GACD,MAAM,qBAAqB,CAAC;QAC1B,IAAI,OAAO,SAAS,EAAE;YACpB,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;YACtC,oBAAM,2BAAC,gCAAyB;;;;;YAChC,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,MAAM;gBACJ,IAAI;oBACF,MAAM,qBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;oBACxC,aAAO,CAAC,OAAO,CAAC;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,aAAO,CAAC,KAAK,CAAC;gBAChB;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAM,oBAAoB;QACxB,MAAM,kBAAkB,QAAQ,MAAM,CACpC,CAAC,SAAW,gBAAgB,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,SAAS;QAGtE,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,MAAM,CAAC;YACnD,oBAAM,2BAAC,gCAAyB;;;;;YAChC,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,MAAM;gBACJ,IAAI;oBACF,MAAM,QAAQ,GAAG,CACf,gBAAgB,GAAG,CAAC,CAAC,SACnB,qBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;oBAGtC,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,gBAAgB,MAAM,CAAC,IAAI,CAAC;oBACpD,mBAAmB,EAAE;oBACrB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,aAAa;oBAC3B,aAAO,CAAC,KAAK,CAAC;gBAChB;YACF;QACF;IACF;IAEA,MAAM,UAA2C;QAC/C;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAM,uBACb,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BAAC,MAAK;4BAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;sCACxC,2BAAC;;8CACC,2BAAC;8CAAK;;;;;;8CACN,2BAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAO;8CAAI,OAAO,KAAK;;;;;;;;;;;;;;;;;;QAIlE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,0BACP,2BAAC,SAAG;oBACF,OAAO,YAAY,SAAS;oBAC5B,MAAM,0BAAY,2BAAC,oBAAa;;;;+CAAM,2BAAC,mBAAY;;;;;8BAElD,YAAY,QAAQ;;;;;;QAG3B;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,yBACP,2BAAC,SAAG;oBAAC,OAAO,WAAW,UAAU;8BAC9B,WAAW,OAAO;;;;;;QAGzB;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,aAAe,IAAI,KAAK,YAAY,kBAAkB;QACjE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC;gBACP,MAAM,OAAO,IAAI,KAAK;gBACtB,MAAM,MAAM,IAAI;gBAChB,MAAM,WAAW,KAAK,KAAK,CACzB,AAAC,CAAA,IAAI,OAAO,KAAK,KAAK,OAAO,EAAC,IAAM;gBAGtC,IAAI,QAAQ;gBACZ,IAAI,WAAW,GAAG,QAAQ;gBAC1B,IAAI,WAAW,IAAI,QAAQ;gBAE3B,qBACE,2BAAC,aAAO;oBAAC,OAAO,KAAK,cAAc;8BACjC,cAAA,2BAAC,SAAG;wBAAC,OAAO;kCACT,aAAa,IAAI,OAAO,CAAC,EAAE,SAAS,EAAE,CAAC;;;;;;;;;;;YAIhD;QACF;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;gBACV,IAAI,CAAC,aAAa,OAAO,SAAS,EAChC,qBAAO,2BAAC;oBAAK,MAAK;8BAAY;;;;;;gBAGhC,qBACE,2BAAC,YAAM;oBACL,MAAK;oBACL,MAAM;oBACN,MAAK;oBACL,oBAAM,2BAAC,qBAAc;;;;;oBACrB,SAAS,IAAM,mBAAmB;8BACnC;;;;;;YAIL;QACF;KACD;IAED,MAAM,eAAe;QACnB;QACA,UAAU,CAAC;YACT,mBAAmB;QACrB;QACA,kBAAkB,CAAC,SAAgC,CAAA;gBACjD,UAAU,OAAO,SAAS;YAC5B,CAAA;IACF;IAEA,qBACE,2BAAC,UAAI;QACH,qBACE,2BAAC,WAAK;;8BACJ,2BAAC;oBAAK,MAAM;8BAAC;;;;;;8BACb,2BAAC;oBAAK,MAAK;;wBAAY;wBAAE,QAAQ,MAAM;wBAAC;;;;;;;;;;;;;;YAI3C,gBAAgB,MAAM,GAAG,KAAK,2BAC7B,2BAAC;gBACC,OAAO;oBACL,cAAc;oBACd,SAAS;oBACT,YAAY;oBACZ,cAAc;gBAChB;0BAEA,cAAA,2BAAC,WAAK;;sCACJ,2BAAC;;gCAAK;gCAAK,gBAAgB,MAAM;gCAAC;;;;;;;sCAClC,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAM;4BACN,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS;sCACV;;;;;;sCAGD,2BAAC,YAAM;4BAAC,MAAK;4BAAQ,SAAS,IAAM,mBAAmB,EAAE;sCAAG;;;;;;;;;;;;;;;;;0BAOlE,2BAAC,WAAK;gBACJ,SAAS;gBACT,YAAY;gBACZ,QAAO;gBACP,SAAS;gBACT,cAAc,YAAY,eAAe;gBACzC,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;oBACtC,UAAU;gBACZ;;;;;;;;;;;;AAIR;GAvOM;KAAA;IAyON,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvRf;;;;;;;;;CASC;;;;4BAmOD;;;eAAA;;;;;;;8BA1NO;6BAYA;wEACoC;iCACf;gFAED;4EACJ;;;;;;;;;;AAEvB,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,YAAM;AAOzB;;CAEC,GACD,MAAM,uBAA4D,CAAC,EACjE,UAAU,EACV,YAAY,EACb;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAS;IACzD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,eAAQ,EAAC;IAEnE;;GAEC,GACD,MAAM,eAAe;QACnB,IAAI,CAAC,YAAY;QAEjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qBAAW,CAAC,cAAc,CAAC;gBAChD,SAAS;gBACT,UAAU;YACZ;YACA,WAAW,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;YACd,WAAW,EAAE;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAA,gBAAS,EAAC;QACR;IACF,GAAG;QAAC;KAAW;IAEf;;GAEC,GACD,MAAM,yBAAyB;QAC7B,yBAAyB;QACzB;QACA;QACA,aAAO,CAAC,OAAO,CAAC;IAClB;IAEA;;GAEC,GACD,MAAM,qBAAqB;QACzB;QACA;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,EAC1C,OAAO;QAGT,SAAS;QACT,MAAM,gBACJ,CAAC,cACD,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE5D,SAAS;QACT,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,OAAO,QAAQ,IAC5C,iBAAiB,cAAc,CAAC,OAAO,QAAQ,IAC/C,iBAAiB,aAAa,OAAO,SAAS,IAC9C,iBAAiB,YAAY,CAAC,OAAO,SAAS;QAEjD,OAAO,iBAAiB;IAC1B;IAEA,OAAO;IACP,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;IAC9D,MAAM,eAAe,QAAQ,MAAM;IAEnC,qBACE,2BAAC;;0BAEC,2BAAC,SAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;gBAAE,OAAO;oBAAE,cAAc;gBAAG;;kCAC/C,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;kCACtB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO;gCACP,QAAO;gCACP,sBAAQ,2BAAC,mBAAY;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCAChD,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;kCACtB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO;gCACP,QAAO;gCACP,sBAAQ,2BAAC,mBAAY;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCAChD,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;kCACtB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,eAAe,IAAI,KAAK,KAAK,CAAC,AAAC,gBAAgB,eAAgB,OAAO;gCAC7E,QAAO;gCACP,YAAY;oCACV,OAAO,gBAAgB,gBAAgB,MAAM,YACtC,gBAAgB,gBAAgB,MAAM,YAAY;gCAC3D;;;;;;;;;;;;;;;;;;;;;;0BAOR,2BAAC,UAAI;gBAAC,OAAO;oBAAE,cAAc;gBAAG;0BAC9B,cAAA,2BAAC,SAAG;oBAAC,SAAQ;oBAAgB,OAAM;;sCACjC,2BAAC,SAAG;sCACF,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,WAAK;wCACJ,aAAY;wCACZ,sBAAQ,2BAAC,qBAAc;;;;;wCACvB,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,OAAO;4CAAE,OAAO;wCAAI;wCACpB,UAAU;;;;;;kDAEZ,2BAAC,YAAM;wCACL,OAAO;wCACP,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAI;;0DAEpB,2BAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,2BAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,2BAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,2BAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,2BAAC;gDAAO,OAAM;0DAAS;;;;;;;;;;;;;;;;;;;;;;;sCAI7B,2BAAC,SAAG;sCACF,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,oBAAM,2BAAC,qBAAc;;;;;wCACrB,SAAS;wCACT,SAAS;kDACV;;;;;;kDAGD,2BAAC,YAAM;wCACL,MAAK;wCACL,oBAAM,2BAAC,sBAAe;;;;;wCACtB,SAAS,IAAM,yBAAyB;kDACzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,2BAAC,mBAAU;gBACT,SAAS;gBACT,SAAS;gBACT,WAAW,CAAA,uBAAA,iCAAA,WAAY,SAAS,KAAI;gBACpC,gBAAgB;;;;;;0BAIlB,2BAAC,uBAAc;gBACb,SAAS;gBACT,UAAU,IAAM,yBAAyB;gBACzC,WAAW;gBACX,mBAAmB,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS;;;;;;;;;;;;AAIvD;GA1LM;KAAA;IA4LN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5Of;;;;;;;;CAQC;;;;4BAqSD;;;eAAA;;;;;;8BA3RO;4BAC2B;6BAc3B;wEACyB;iCACJ;;;;;;;;;;AAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAO1B;;CAEC,GACD,MAAM,eAA4C,CAAC,EACjD,UAAU,EACV,YAAY,EACb;;IACC,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAE3B,IAAI,CAAC,YACH,OAAO;IAGT;;GAEC,GACD,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,aAAa;QACjB,KAAK,cAAc,CAAC;YAClB,MAAM,WAAW,IAAI;YACrB,aAAa,WAAW,WAAW,IAAI;QACzC;QACA,oBAAoB;IACtB;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,YAAY;YACZ,MAAM,qBAAW,CAAC,iBAAiB,CAAC;YACpC,aAAO,CAAC,OAAO,CAAC;YAChB,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB;QACvB,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,uBACE,2BAAC;;kCACC,2BAAC;;4BAAU;0CACD,2BAAC;gCAAK,MAAM;;oCAAC;oCAAE,WAAW,IAAI;oCAAC;;;;;;;4BAAQ;;;;;;;kCAEjD,2BAAC;wBAAU,MAAK;;0CACd,2BAAC,gCAAyB;;;;;4BAAG;;;;;;;kCAE/B,2BAAC;;0CACC,2BAAC;0CAAG;;;;;;0CACJ,2BAAC;0CAAG;;;;;;0CACJ,2BAAC;0CAAG;;;;;;;;;;;;;;;;;;YAIV,oBAAM,2BAAC,gCAAyB;gBAAC,OAAO;oBAAE,OAAO;gBAAU;;;;;;YAC3D,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,MAAM;gBACJ,IAAI;oBACF,YAAY;oBACZ,MAAM,qBAAW,CAAC,iBAAiB;oBACnC,aAAO,CAAC,OAAO,CAAC;oBAChB,gBAAgB;oBAChB,gBAAgB,CAAC,IAAO,CAAA;4BAAE,GAAG,CAAC;4BAAE,aAAa;wBAAU,CAAA;oBACvD,YAAY;oBACZ,YAAO,CAAC,IAAI,CAAC;gBACf,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,aAAO,CAAC,KAAK,CAAC;gBAChB,SAAU;oBACR,YAAY;gBACd;YACF;QACF;IACF;IAEA,qBACE,2BAAC;;0BAEC,2BAAC,SAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;gBAAE,OAAO;oBAAE,cAAc;gBAAG;;kCAC/C,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW;gCAC7B,QAAO;gCACP,sBAAQ,2BAAC,mBAAY;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCAChD,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW,SAAS;gCACtC,sBAAQ,2BAAC,uBAAgB;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCACpD,YAAY;oCAAE,OAAO;oCAAW,UAAU;gCAAG;;;;;;;;;;;;;;;;kCAInD,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW,SAAS;gCACtC,sBAAQ,2BAAC,uBAAgB;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCACpD,YAAY;oCAAE,OAAO;oCAAW,UAAU;gCAAG;;;;;;;;;;;;;;;;;;;;;;0BAOrD,2BAAC,UAAI;gBACH,qBACE,2BAAC,WAAK;;sCACJ,2BAAC,mBAAY;;;;;wBAAG;;;;;;;gBAIpB,qBACE,2BAAC,YAAM;oBACL,MAAK;oBACL,oBAAM,2BAAC,mBAAY;;;;;oBACnB,SAAS;8BACV;;;;;;gBAIH,OAAO;oBAAE,cAAc;gBAAG;0BAE1B,cAAA,2BAAC,kBAAY;oBAAC,QAAQ;oBAAG,QAAQ;;sCAC/B,2BAAC,kBAAY,CAAC,IAAI;4BAAC,OAAM;sCACvB,cAAA,2BAAC;gCAAK,MAAM;0CAAE,WAAW,IAAI;;;;;;;;;;;sCAE/B,2BAAC,kBAAY,CAAC,IAAI;4BAAC,OAAM;sCACtB,WAAW,WAAW,kBACrB,2BAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;sCAG3B,2BAAC,kBAAY,CAAC,IAAI;4BAAC,OAAM;sCACvB,cAAA,2BAAC;gCAAK,IAAI;0CAAE,WAAW,EAAE;;;;;;;;;;;sCAE3B,2BAAC,kBAAY,CAAC,IAAI;4BAAC,OAAM;sCACvB,cAAA,2BAAC;0CAAM,WAAW,SAAS,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAM1C,2BAAC,UAAI;gBACH,qBACE,2BAAC,WAAK;;sCACJ,2BAAC,gCAAyB;4BAAC,OAAO;gCAAE,OAAO;4BAAU;;;;;;sCACrD,2BAAC;4BAAK,MAAK;sCAAS;;;;;;;;;;;;0BAIxB,cAAA,2BAAC,WAAK;oBAAC,WAAU;oBAAW,MAAK;oBAAQ,OAAO;wBAAE,OAAO;oBAAO;8BAC9D,cAAA,2BAAC;;0CACC,2BAAC;gCAAM,OAAO;gCAAG,MAAK;0CAAS;;;;;;0CAC/B,2BAAC;gCAAU,MAAK;0CAAY;;;;;;0CAG5B,2BAAC,YAAM;gCACL,MAAM;gCACN,oBAAM,2BAAC,qBAAc;;;;;gCACrB,SAAS;gCACT,SAAS;0CACV;;;;;;;;;;;;;;;;;;;;;;0BAQP,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,oBAAoB;gBACpC,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,UAAI;oBAAC,MAAM;oBAAM,QAAO;oBAAW,UAAU;;sCAC5C,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAI,SAAS;gCAAgB;6BACrC;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;sCAErB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCAAC;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BAAE;sCAEhD,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;gCACZ,SAAS;gCACT,WAAW;;;;;;;;;;;sCAGf,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,SAAS,IAAM,oBAAoB;kDAAQ;;;;;;kDACnD,2BAAC,YAAM;wCACL,MAAK;wCACL,UAAS;wCACT,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;kDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA1PM;;QAIwB,aAAQ;QAIrB,UAAI,CAAC;;;KARhB;IA4PN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7Sf;;;;;;;;;CASC;;;;4BA0JD;;;eAAA;;;;;;;8BAnJO;sCACuB;4BACL;6BAQlB;wEACoC;iCACf;sFAEK;8EACR;;;;;;;;;;AAEzB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC;;CAEC,GACD,MAAM,qBAA+B;;IACnC,MAAM,EAAE,YAAY,EAAE,GAAG,IAAA,aAAQ,EAAC;IAClC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAE3C;;GAEC,GACD,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,qBAAW,CAAC,oBAAoB;YACrD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,cAAc;IACd,MAAM,sBAAsB,CAAA,uBAAA,iCAAA,WAAY,SAAS,KAAI;IAErD,gBAAgB;IAChB,IAAI,CAAC,WAAW,CAAC,YACf,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,WAAK;YACJ,SAAQ;YACR,aAAY;YACZ,MAAK;YACL,QAAQ;;;;;;;;;;;IAMhB,iBAAiB;IACjB,IAAI,CAAC,WAAW,cAAc,CAAC,qBAC7B,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,WAAK;YACJ,SAAQ;YACR,aAAY;YACZ,MAAK;YACL,QAAQ;;;;;;;;;;;IAMhB,UAAU;IACV,MAAM,WAAW;QACf;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,mBAAY;;;;;oBAAG;;;;;;;YAIpB,wBACE,2BAAC,6BAAoB;gBACnB,YAAY;gBACZ,cAAc;;;;;;QAGpB;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,sBAAe;;;;;oBAAG;;;;;;;YAIvB,wBACE,2BAAC,qBAAY;gBACX,YAAY;gBACZ,cAAc;;;;;;QAGpB;KACD;IAED,qBACE,2BAAC,4BAAa;QACZ,qBACE,2BAAC,WAAK;;8BACJ,2BAAC,mBAAY;;;;;gBAAG;;;;;;;QAIpB,UAAU,aAAa,CAAC,KAAK,EAAE,WAAW,IAAI,CAAC,CAAC,GAAG;QACnD,OACE,CAAA,uBAAA,iCAAA,WAAY,SAAS,mBACnB,2BAAC,WAAK;;8BACJ,2BAAC,oBAAa;oBAAC,OAAO;wBAAE,OAAO;oBAAU;;;;;;8BACzC,2BAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;QAI7B,SAAS;kBAER,4BACC,2BAAC,UAAI;sBACH,cAAA,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,MAAK;gBACL,aAAa;oBAAE,cAAc;gBAAG;;;;;;;;;;;;;;;;AAM5C;GA3HM;;QACqB,aAAQ;;;KAD7B;IA6HN,WAAe"}